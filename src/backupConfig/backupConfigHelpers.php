<?php
/**
 * Backup Configuration Helper Functions
 */

/**
 * Get currently configured modules for backup
 * 
 * @return array Array of configured modules and settings
 */
function getBackupModules() {
    $configFile = '/var/www/html/backups/backup-modules.conf';
    $backupModules = [
        'backup_type' => 'local',
        'apm' => 'no',
        'npm' => 'no',
        'nta' => 'no',
        'spm' => 'no',
        'elm' => 'no',
        'ncm' => 'no',
        'nsm' => 'no',
        'alm' => 'no',
        'scp_server' => '',
        'scp_user' => '',
        'scp_password' => '',
        'scp_folder' => '',
        'scp_delete_local' => 'no',
        'ftp_server' => '',
        'ftp_user' => '',
        'ftp_password' => '',
        'ftp_folder' => '',
        'ftp_delete_local' => 'no',
        'auto_schedule' => 'no',
        'auto_frequency' => 'daily',
        'auto_time' => '02:00'
    ];

    if (file_exists($configFile)) {
        $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            $parts = explode('=', $line, 2);
            if (count($parts) === 2) {
                $key = trim($parts[0]);
                $value = trim($parts[1]);
                if (array_key_exists($key, $backupModules)) {
                    $backupModules[$key] = $value;
                }
            }
        }
    }

    // After loading configuration from backup-modules.conf, attempt to read the cron file to
    // get authoritative schedule information (in case the config file is out of sync).
    $cronFile = '/etc/cron.d/blesk-backup';
    if (file_exists($cronFile) && is_readable($cronFile)) {
        $cronLines = file($cronFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($cronLines as $cLine) {
            // Skip comments
            if (strpos(trim($cLine), '#') === 0) {
                continue;
            }
            // Look for the line that runs autoBackupCron.php
            if (strpos($cLine, 'autoBackupCron.php') !== false) {
                // Cron format: min hour dom mon dow user command
                $parts = preg_split('/\s+/', $cLine, 7);
                if (count($parts) >= 7) {
                    list($min, $hour, $dom, $mon, $dow) = array_slice($parts, 0, 5);

                    // Determine frequency
                    if ($dom !== '*' && $dow === '*') {
                        $backupModules['auto_frequency'] = 'monthly';
                    } elseif ($dow !== '*' && $dom === '*') {
                        $backupModules['auto_frequency'] = 'weekly';
                    } else {
                        $backupModules['auto_frequency'] = 'daily';
                    }

                    // Format time HH:MM (pad with leading zeros)
                    $hourPadded = str_pad($hour, 2, '0', STR_PAD_LEFT);
                    $minPadded  = str_pad($min, 2, '0', STR_PAD_LEFT);
                    $backupModules['auto_time'] = $hourPadded . ':' . $minPadded;

                    // If we found a valid line, auto_schedule is yes
                    $backupModules['auto_schedule'] = 'yes';
                }

                // We found the relevant line; no need to parse further.
                break;
            }
        }
    } else {
        // Cron file does not exist; assume auto scheduling disabled
        $backupModules['auto_schedule'] = 'no';
    }

    return $backupModules;
}

/**
 * Check if a backup is in progress
 * 
 * @return bool True if backup is in progress, false otherwise
 */
function isBackupInProgress() {
    return file_exists('/tmp/blesk-backup.lock');
}

/**
 * Get available backup files
 * 
 * @return array List of backup files with details
 */
function getBackupFiles() {
    $backupPath = '/var/www/html/backups';
    $files = [];
    
    if (is_dir($backupPath)) {
        $dirFiles = scandir($backupPath);
        foreach ($dirFiles as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $filePath = $backupPath . '/' . $file;
            if (is_file($filePath) && (
                pathinfo($file, PATHINFO_EXTENSION) === 'gz' || 
                pathinfo($file, PATHINFO_EXTENSION) === 'tgz' || 
                substr($file, -7) === '.tar.gz'
            )) {
                $files[] = [
                    'name' => $file,
                    'size' => filesize($filePath),
                    'date' => date("Y-m-d H:i:s", filemtime($filePath))
                ];
            }
        }
    }
    
    // Sort files by modification time (newest first)
    usort($files, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
    
    return $files;
} 