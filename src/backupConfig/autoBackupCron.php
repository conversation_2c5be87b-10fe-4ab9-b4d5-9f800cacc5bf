<?php
/**
 * Automatic Backup Script (for cron)
 *
 * This script is executed by cron (see /etc/cron.d/blesk-backup) to perform
 * a backup using the settings saved via the web interface.
 *
 * Usage: php autoBackupCron.php
 */

// Ensure errors are logged but not output (cron-friendly)
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once __DIR__ . '/backupConfigHelpers.php';

// Prevent concurrent backups
if (isBackupInProgress()) {
    error_log('[autoBackupCron] Backup already in progress, skipping scheduled run.');
    exit;
}

// Retrieve saved configuration
$config = getBackupModules();
$backupType = isset($config['backup_type']) ? $config['backup_type'] : 'local';

// Build selected modules array from config
$selectedModules = [];
foreach (['apm', 'npm', 'nta', 'spm', 'elm', 'ncm', 'nsm', 'alm'] as $module) {
    $selectedModules[$module] = ($config[$module] === 'yes') ? 'yes' : 'no';
}

// SCP and FTP settings arrays (populate regardless of backup type for convenience)
$scpSettings = [
    'server' => $config['scp_server'],
    'user' => $config['scp_user'],
    'password' => $config['scp_password'],
    'folder' => $config['scp_folder'],
    'delete_local' => $config['scp_delete_local']
];

$ftpSettings = [
    'server' => $config['ftp_server'],
    'user' => $config['ftp_user'],
    'password' => $config['ftp_password'],
    'folder' => $config['ftp_folder'],
    'delete_local' => $config['ftp_delete_local']
];

// Helper function to perform backup (logic adapted from backupConfigHandler.php)
function performBackup($backupType, $selectedModules, $scpSettings, $ftpSettings)
{
    // Prepare module arguments for blesk-backup
    $args = '';
    $moduleCount = 0;
    foreach (['apm', 'npm', 'nta', 'spm', 'elm', 'ncm', 'nsm', 'alm'] as $module) {
        if (isset($selectedModules[$module]) && $selectedModules[$module] === 'yes') {
            $args .= " $module";
            $moduleCount++;
        }
    }

    if ($moduleCount === 0) {
        error_log('[autoBackupCron] No modules selected for backup. Aborting.');
        return false;
    }

    // Run local backup first
    $backupCommand = "sudo /usr/bin/blesk-backup$args > /dev/null 2>&1";
    exec($backupCommand, $backupOutput, $backupStatus);
    if ($backupStatus !== 0) {
        error_log('[autoBackupCron] Local backup command failed. Status: ' . $backupStatus . ' Output: ' . implode("\n", $backupOutput));
        return false;
    }

    // Locate newest backup file
    $backupDir = '/var/www/html/backups';
    $files = glob($backupDir . '/*.tar.gz');
    if (empty($files)) {
        error_log('[autoBackupCron] No backup file found after running blesk-backup.');
        return false;
    }
    usort($files, function ($a, $b) {
        return filemtime($b) - filemtime($a);
    });
    $localBackupFile = $files[0];
    $localBackupFilename = basename($localBackupFile);

    // Handle remote transfer if requested
    if ($backupType === 'scp') {
        if (empty($scpSettings['server']) || empty($scpSettings['user']) || empty($scpSettings['folder'])) {
            error_log('[autoBackupCron] SCP settings incomplete. Skipping remote transfer.');
            return true; // local backup succeeded
        }

        $scpServer = escapeshellarg($scpSettings['server']);
        $scpUser = escapeshellarg($scpSettings['user']);
        $scpPassword = $scpSettings['password'];
        $scpRemoteFolder = escapeshellarg(rtrim($scpSettings['folder'], '/') . '/' . $localBackupFilename);
        $deleteLocal = ($scpSettings['delete_local'] === 'yes');

        if (!empty($scpPassword)) {
            $scpCommand = sprintf(
                'sshpass -p %s scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null %s %s@%s:%s',
                escapeshellarg($scpPassword),
                escapeshellarg($localBackupFile),
                $scpUser,
                $scpServer,
                $scpRemoteFolder
            );
        } else {
            $scpCommand = sprintf(
                'scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null %s %s@%s:%s',
                escapeshellarg($localBackupFile),
                $scpUser,
                $scpServer,
                $scpRemoteFolder
            );
        }

        exec($scpCommand . ' > /dev/null 2>&1', $scpOutput, $scpStatus);
        if ($scpStatus !== 0) {
            error_log('[autoBackupCron] SCP transfer failed. Status: ' . $scpStatus . ' Output: ' . implode("\n", $scpOutput));
        } else {
            if ($deleteLocal) {
                if (!unlink($localBackupFile)) {
                    error_log('[autoBackupCron] Failed to delete local backup file after SCP transfer.');
                }
            }
        }
    } elseif ($backupType === 'ftp') {
        if (empty($ftpSettings['server']) || empty($ftpSettings['user']) || empty($ftpSettings['folder'])) {
            error_log('[autoBackupCron] FTP settings incomplete. Skipping remote transfer.');
            return true;
        }

        $ftpCommand = "ftp -inv {$ftpSettings['server']} <<EOF\n";
        $ftpCommand .= "user {$ftpSettings['user']} {$ftpSettings['password']}\n";
        $ftpCommand .= "cd {$ftpSettings['folder']}\n";
        $ftpCommand .= "lcd /var/www/html/backups\n";
        $ftpCommand .= "put {$localBackupFilename}\n";
        $ftpCommand .= "bye\nEOF";

        $tempScript = tempnam(sys_get_temp_dir(), 'ftp_script_');
        file_put_contents($tempScript, $ftpCommand);
        chmod($tempScript, 0700);
        exec($tempScript . ' 2>&1', $ftpOutput, $ftpStatus);
        unlink($tempScript);

        if ($ftpStatus !== 0) {
            error_log('[autoBackupCron] FTP transfer failed. Status: ' . $ftpStatus . ' Output: ' . implode("\n", $ftpOutput));
        } else {
            if ($ftpSettings['delete_local'] === 'yes') {
                if (!unlink($localBackupFile)) {
                    error_log('[autoBackupCron] Failed to delete local backup file after FTP transfer.');
                }
            }
        }
    }

    return true;
}

// Execute backup
if (!performBackup($backupType, $selectedModules, $scpSettings, $ftpSettings)) {
    // Failure already logged via error_log
    exit(1);
}

exit(0); 