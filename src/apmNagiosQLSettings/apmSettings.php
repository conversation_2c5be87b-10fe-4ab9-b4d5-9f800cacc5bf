<!-- APM Content Panes -->
<?php
$apmTabs = [
    'hosts' => [
        'icon' => 'fa-server',
        'title' => 'APM Hosts',
        'url' => '/nagiosql/admin/hosts.php'
    ],
    'services' => [
        'icon' => 'fa-cog',
        'title' => 'APM Services',
        'url' => '/nagiosql/admin/services.php'
    ],
    'host-groups' => [
        'icon' => 'fa-object-group',
        'title' => 'APM Host Groups',
        'url' => '/nagiosql/admin/hostgroups.php'
    ],
    'service-groups' => [
        'icon' => 'fa-object-ungroup',
        'title' => 'APM Service Groups',
        'url' => '/nagiosql/admin/servicegroups.php'
    ],
    'contacts' => [
        'icon' => 'fa-user',
        'title' => 'APM Contacts',
        'url' => '/nagiosql/admin/contacts.php'
    ],
    'contact-groups' => [
        'icon' => 'fa-users',
        'title' => 'APM Contact Groups',
        'url' => '/nagiosql/admin/contactgroups.php'
    ],
    'time' => [
        'icon' => 'fa-clock-o',
        'title' => 'APM Time Periods',
        'url' => '/nagiosql/admin/timeperiods.php'
    ],
    'commands' => [
        'icon' => 'fa-code',
        'title' => 'APM Commands',
        'url' => '/nagiosql/admin/checkcommands.php'
    ],
    'control' => [
        'icon' => 'fa-check-circle',
        'title' => 'Control',
        'url' => '/nagiosql/admin/verify.php'
    ],
    'other' => [
        'icon' => 'fa-sliders',
        'title' => 'APM Other Settings',
        'url' => '/nagiosql/admin/settings.php'
    ]
];

foreach ($apmTabs as $id => $tab) {
    echo "<div id=\"apm-{$id}\" class=\"tab-content-pane\">\n";
    echo "    <h2 style=\"display:flex;align-items:center;gap:8px;\">";
    echo "        <i class=\"fa {$tab['icon']}\"></i> {$tab['title']}";
    echo "        <button type=\"button\" class=\"apm-refresh-btn\" data-pane-id=\"apm-{$id}\" title=\"Refresh\" style=\"background:none;border:none;cursor:pointer;color:inherit;font-size:inherit;padding:0;\">";
    echo "            <i class=\"fa fa-refresh\"></i>";
    echo "        </button>";
    echo "    </h2>\n";
    echo "    <div class=\"iframe-container\" data-url=\"https://{$_SERVER['SERVER_ADDR']}{$tab['url']}\"></div>\n";
    echo "</div>\n\n";
}
?>