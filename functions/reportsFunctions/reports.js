(function() {
    // Helper: convert seconds to formatted duration (h m s)
    function formatDuration(sec) {
        const h = Math.floor(sec / 3600);
        const m = Math.floor((sec % 3600) / 60);
        const s = sec % 60;
        return `${h}h ${m}m ${s}s`;
    }

    // Helper: local ISO string without seconds for <input type="datetime-local">
    function toLocalIso(dateObj) {
        const tzOffset = dateObj.getTimezoneOffset() * 60000;
        return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0, 16);
    }

    // Helper: fetch hostname -> IP address map via Nagios objectjson API
    async function fetchHostIpMap(){
        try {
            const resp = await fetch('/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true', { credentials: 'include' });
            if(!resp.ok) throw new Error(resp.statusText);
            const js = await resp.json();
            const map = {};
            const hostlist = js?.data?.hostlist || {};
            Object.entries(hostlist).forEach(([hostname, obj])=>{
                if(obj && obj.address){
                    map[hostname] = obj.address;
                }
            });
            return map;
        } catch(err){
            console.warn('Failed to fetch host IP map', err);
            return {}; // graceful fallback
        }
    }

    // DOM ready
    document.addEventListener('DOMContentLoaded', () => {
        const startInput = document.getElementById('report-start');
        const endInput   = document.getElementById('report-end');
        const typeSel    = document.getElementById('report-type');
        const hostgroupSel = document.getElementById('report-hostgroup');
        const genBtn     = document.getElementById('report-generate');
        const exportBtn  = document.getElementById('report-export');

        const saveBtn    = document.getElementById('report-save');
        const scheduleBtn  = document.getElementById('report-schedule');
        const viewSavedBtn = document.getElementById('view-saved-reports');
        const contentDiv = document.getElementById('reports-content');
        const statusFilterContainer = document.getElementById('reports-status-filters');
        const srDisableBtn = document.getElementById('sr-disable');
        const srStatus     = document.getElementById('sr-status');

        // Initialise date pickers using server time if possible
        function initDates() {
            const progUrl = '/nagios/cgi-bin/statusjson.cgi?query=programstatus';
            fetch(progUrl, { credentials: 'include' })
                .then(r => r.json())
                .then(js => js?.data?.programstatus?.current_time)
                .then(serverSec => {
                    const endDate = serverSec ? new Date(serverSec * 1000) : new Date();
                    endInput.value = toLocalIso(endDate);
                    startInput.value = toLocalIso(new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
                })
                .catch(() => {
                    const now = new Date();
                    endInput.value = toLocalIso(now);
                    startInput.value = toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
                });
        }

        // ------------------ Dropdown population helpers ------------------

        // Remove all options except the first ("All")
        function resetSelectOptions(sel) {
            while (sel.options.length > 1) sel.remove(1);
        }

        // Generic helper to append unique option values keeping "All" first
        function appendOptions(arr) {
            const existing = new Set(Array.from(hostgroupSel.options).map(o => o.value));
            arr.sort().forEach(v => {
                if (!existing.has(v)) {
                    const opt = document.createElement('option');
                    opt.value = v;
                    opt.textContent = v;
                    hostgroupSel.appendChild(opt);
                }
            });
        }

        // Populate hostgroups dropdown using availability endpoint only
        function populateHostgroups() {
            resetSelectOptions(hostgroupSel);
            const nowSec = Math.floor(Date.now() / 1000);
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    const arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                    if (arr.length) {
                        appendOptions(arr);
                    } else if (typeof fetchAllHostGroups === 'function') {
                        // ultimate fallback DB
                        fetchAllHostGroups().then(appendOptions);
                    }
                })
                .catch(() => {
                    if (typeof fetchAllHostGroups === 'function') fetchAllHostGroups().then(appendOptions);
                });
        }

        // Populate hosts dropdown using availability endpoint
        function populateHosts() {
            resetSelectOptions(hostgroupSel);
            const nowSec = Math.floor(Date.now() / 1000);
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    const arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                    if (arr.length) {
                        appendOptions(arr);
                    } else if (typeof fetchAllHosts === 'function') {
                        // If there's a helper available for full host list
                        fetchAllHosts().then(appendOptions);
                    }
                })
                .catch(() => {
                    if (typeof fetchAllHosts === 'function') fetchAllHosts().then(appendOptions);
                });
        }

        // Update dropdown + label based on selected report type
        function refreshEntityDropdown() {
            const lbl = document.querySelector('label[for="report-hostgroup"]');
            if (typeSel.value === 'services') {
                lbl.textContent = 'Host';
                populateHosts();
            } else {
                lbl.textContent = 'Hostgroup';
                populateHostgroups();
            }
        }

        initDates();
        refreshEntityDropdown();

        // Attach handlers
        genBtn.addEventListener('click', generateReport);
        exportBtn.addEventListener('click', exportCsv);
        if(saveBtn) saveBtn.addEventListener('click', saveReport);
        if(viewSavedBtn) viewSavedBtn.addEventListener('click', openSavedReportsModal);
        if(scheduleBtn){
            scheduleBtn.addEventListener('click', ()=>{
                const scheduleModal = document.getElementById('scheduleReportModal');
                if(!scheduleModal) return;

                // Show modal first (UX) then attempt to load config
                scheduleModal.style.display = 'flex';

                // Reset form to defaults & status message
                document.getElementById('sr-email').value = '';
                document.getElementById('sr-frequency').value = 'daily';
                document.getElementById('sr-time').value = '00:05';
                document.getElementById('sr-range').value = 1;
                // Reset status checkboxes (select all by default)
                document.querySelectorAll('#sr-host-statuses input').forEach(cb => cb.checked = true);
                document.querySelectorAll('#sr-svc-statuses input').forEach(cb => cb.checked = true);
                if(srDisableBtn) srDisableBtn.style.display = 'none';
                if(srStatus){
                    srStatus.textContent = 'Checking current schedule…';
                    srStatus.className = 'sr-status';
                }

                // Fetch current cron config
                fetch('functions/reportsFunctions/getReportCron.php')
                    .then(r=>r.json())
                    .then(js=>{
                        if(!js.success) return;
                        if(js.enabled && js.config){
                            const cfg = js.config;
                            if(cfg.email) document.getElementById('sr-email').value = cfg.email;
                            if(cfg.frequency) document.getElementById('sr-frequency').value = cfg.frequency;
                            if(cfg.time) document.getElementById('sr-time').value = cfg.time;
                            if(cfg.range) document.getElementById('sr-range').value = cfg.range;
                            // Apply stored status filters
                            if(cfg.hostStatuses){
                                const arr = cfg.hostStatuses.split(',');
                                document.querySelectorAll('#sr-host-statuses input').forEach(cb=>{cb.checked = arr.includes(cb.value);});
                            }
                            if(cfg.svcStatuses){
                                const arrS = cfg.svcStatuses.split(',');
                                document.querySelectorAll('#sr-svc-statuses input').forEach(cb=>{cb.checked = arrS.includes(cb.value);});
                            }
                            if(srDisableBtn) srDisableBtn.style.display = 'inline-block';
                            if(srStatus){
                                srStatus.textContent = 'Report is currently scheduled';
                                srStatus.classList.add('scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.warn('Failed to fetch cron config', err);
                        if(srStatus){
                            srStatus.textContent = 'Unable to retrieve schedule status';
                        }
                    });
            });
        }

        // update filters when type changes
        typeSel.addEventListener('change', () => {
            buildStatusFilters(typeSel.value);
            refreshEntityDropdown();
        });

        // ------------- Status options & state (moved up to avoid TDZ) -------------
        const hostStatusOptions = [
            { key: 'up', label: 'Up', class: 'ok' },
            { key: 'down', label: 'Down', class: 'critical' },
            { key: 'unreachable', label: 'Unreach', class: 'unknown' }
        ];

        const serviceStatusOptions = [
            { key: 'ok', label: 'OK', class: 'ok' },
            { key: 'warning', label: 'Warn', class: 'warning' },
            { key: 'critical', label: 'Crit', class: 'critical' },
            { key: 'unknown', label: 'Unk', class: 'unknown' }
        ];

        let activeStatuses = new Set();

        // Build filters initially now that constants exist
        buildStatusFilters(typeSel.value);

        // Main generator
        async function generateReport() {
            const type = typeSel.value; // hostgroups | services
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs   = Math.floor(new Date(endInput.value).getTime() / 1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                alert('Invalid time range');
                return;
            }

            let url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=${encodeURIComponent(type)}&starttime=${startTs}&endtime=${endTs}`;
            const entityVal = hostgroupSel.value;
            if (entityVal && entityVal !== 'all') {
                if (type === 'services') {
                    url += `&hostname=${encodeURIComponent(entityVal)}`;
                } else {
                    url += `&hostgroup=${encodeURIComponent(entityVal)}`;
                }
            }

            // Loading indicator
            contentDiv.innerHTML = '<div class="reports-loading"><i class="fa fa-spinner fa-spin"></i> Loading...</div>';

            try {
                // Fetch availability data and host IP map in parallel for efficiency
                const [resp, hostIpMap] = await Promise.all([
                    fetch(url, { credentials: 'include' }),
                    fetchHostIpMap()
                ]);
                if (!resp.ok) throw new Error(resp.statusText);
                const data = await resp.json();
                renderTable(data, type, hostIpMap);
                applyStatusFilter();
            } catch (err) {
                console.error('Report fetch error', err);
                contentDiv.innerHTML = '<div class="reports-error">Error fetching report data</div>';
            }
        }

        // Render functions
        function renderTable(json, type, ipMap = {}) {
            let html = '';
            let tocItems = [];

            // Generate table of contents and main content
            if (type === 'services') {
                const services = json?.data?.services || [];
                if (!services.length) {
                    contentDiv.innerHTML = '<div class="reports-empty">No data for selected parameters</div>';
                    return;
                }

                // --- Build host -> services map -----
                const hostMap = services.reduce((map, svc) => {
                    if (!map[svc.host_name]) map[svc.host_name] = [];
                    map[svc.host_name].push(svc);
                    return map;
                }, {});

                // Add main section to TOC
                tocItems.push({
                    id: 'services-section',
                    title: 'Services Section',
                    level: 1
                });

                // Add hosts to TOC
                Object.keys(hostMap).sort().forEach(host => {
                    const ip = ipMap[host];
                    const hostDisplay = ip && ip !== host ? `${host} (${ip})` : host;
                    tocItems.push({
                        id: `host-${host.replace(/[^a-zA-Z0-9]/g, '_')}`,
                        title: hostDisplay,
                        level: 2
                    });
                });

                // Generate TOC HTML
                html += generateTableOfContents(tocItems);

                // Add main section title
                html += '<h2 class="report-main-title" id="services-section">Services Section</h2>';

                // --- Render each host section -----
                Object.keys(hostMap).sort().forEach(host => {
                    const ip = ipMap[host];
                    const hostDisplay = ip && ip !== host ? `${host} (${ip})` : host;
                    html += `<h3 class="report-title" id="host-${host.replace(/[^a-zA-Z0-9]/g, '_')}">${hostDisplay}</h3>`;
                    html += '<table class="report-table service-table"><thead><tr><th>Service</th><th>OK %</th><th>Warn %</th><th>Crit %</th><th>Unk %</th></tr></thead><tbody>';

                    hostMap[host].forEach(svc => {
                        const total = (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);
                        const pctNumOk = total ? (+svc.time_ok/total)*100 : 0;
                        const pctNumWarn = total ? (+svc.time_warning/total)*100 : 0;
                        const pctNumCrit = total ? (+svc.time_critical/total)*100 : 0;
                        const pctNumUnk = total ? (+svc.time_unknown/total)*100 : 0;
                        const fmt = n => n.toFixed(1);
                        const status = (+svc.time_critical||0)>0 ? 'critical' : ((+svc.time_warning||0)>0 ? 'warning' : ((+svc.time_unknown||0)>0 ? 'unknown' : 'ok'));
                        html += `<tr data-status="${status}">
                            <td>${svc.description}</td>
                            <td${pctNumOk>0? ' class="ok"':''}>${fmt(pctNumOk)}</td>
                            <td${pctNumWarn>0? ' class="warning"':''}>${fmt(pctNumWarn)}</td>
                            <td${pctNumCrit>0? ' class="critical"':''}>${fmt(pctNumCrit)}</td>
                            <td${pctNumUnk>0? ' class="unknown"':''}>${fmt(pctNumUnk)}</td>
                        </tr>`;
                    });
                    html += '</tbody></table>';
                });
            } else if (type === 'hostgroups') {
                const hostgroups = json?.data?.hostgroups || [];
                // Handle both array and single-object responses
                const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (json?.data?.hostgroup ? [json.data.hostgroup] : []);

                if (!grpArr.length) {
                    contentDiv.innerHTML = '<div class="reports-empty">No data for selected parameters</div>';
                    return;
                }

                // Add main section to TOC
                tocItems.push({
                    id: 'hosts-section',
                    title: 'Hosts Section',
                    level: 1
                });

                // Add hostgroups to TOC
                grpArr.forEach(g => {
                    const hostList = g.hosts || [];
                    if (hostList.length) { // Only add if hostgroup has hosts
                        tocItems.push({
                            id: `hostgroup-${g.name.replace(/[^a-zA-Z0-9]/g, '_')}`,
                            title: g.name,
                            level: 2
                        });
                    }
                });

                // Generate TOC HTML
                html += generateTableOfContents(tocItems);

                // Add main section title
                html += '<h2 class="report-main-title" id="hosts-section">Hosts Section</h2>';

                grpArr.forEach(g => {
                    const hostList = g.hosts || [];
                    if (!hostList.length) return; // Skip empty hostgroups

                    html += `<h3 class="report-title" id="hostgroup-${g.name.replace(/[^a-zA-Z0-9]/g, '_')}">${g.name}</h3>`;
                    html += '<table class="report-table hostgroup-table"><thead><tr><th>Host</th><th>Up %</th><th>Down %</th><th>Unreachable %</th></tr></thead><tbody>';
                    hostList.forEach(h => {
                        const total = (+h.time_up || 0) + (+h.time_down || 0) + (+h.time_unreachable || 0);
                        const pctNumUp = total ? (+h.time_up/total)*100 : 0;
                        const pctNumDown = total ? (+h.time_down/total)*100 : 0;
                        const pctNumUnreach = total ? (+h.time_unreachable/total)*100 : 0;
                        const fmtH = n=>n.toFixed(1);
                        const status = (+h.time_down||0)>0? 'down' : ((+h.time_unreachable||0)>0 ? 'unreachable':'up');
                        const ip = ipMap[h.name];
                        const hostDisplay = ip && ip !== h.name ? `${h.name} (${ip})` : h.name;
                        html += `<tr data-status="${status}">
                                <td>${hostDisplay}</td>
                                <td${pctNumUp>0? ' class="ok"':''}>${fmtH(pctNumUp)}</td>
                                <td${pctNumDown>0? ' class="critical"':''}>${fmtH(pctNumDown)}</td>
                                <td${pctNumUnreach>0? ' class="unknown"':''}>${fmtH(pctNumUnreach)}</td>
                            </tr>`;
                    });
                    html += '</tbody></table>';
                });
            } else {
                contentDiv.innerHTML = '<div class="reports-empty">Unsupported report type</div>';
                return;
            }
            contentDiv.innerHTML = html;

            // Add smooth scrolling for table of contents links
            setupTableOfContentsNavigation();
        }

        // Helper function to generate table of contents HTML
        function generateTableOfContents(tocItems) {
            if (!tocItems.length) return '';

            let tocHtml = '<div class="table-of-contents">';
            tocHtml += '<h3 class="toc-title">Table of Contents</h3>';
            tocHtml += '<ul class="toc-list">';

            tocItems.forEach(item => {
                const levelClass = item.level === 1 ? 'toc-level-1' : 'toc-level-2';
                tocHtml += `<li class="${levelClass}">
                    <a href="#${item.id}" class="toc-link">${item.title}</a>
                </li>`;
            });

            tocHtml += '</ul></div>';
            return tocHtml;
        }

        // Setup smooth scrolling for table of contents navigation
        function setupTableOfContentsNavigation() {
            const tocLinks = contentDiv.querySelectorAll('.toc-link');
            tocLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // ---------------- Status Filtering ------------------

        function buildStatusFilters(type){
            statusFilterContainer.innerHTML = '';
            const opts = type === 'services' ? serviceStatusOptions : hostStatusOptions;
            activeStatuses = new Set(opts.map(o=>o.key));
            opts.forEach(o=>{
                const btn = document.createElement('button');
                btn.className = `reports-status-filter ${o.class} active`;
                btn.dataset.statusKey = o.key;
                btn.title = o.label;
                btn.textContent = o.label;
                btn.addEventListener('click', ()=>{
                    if (btn.classList.contains('active')){
                        btn.classList.remove('active');
                        activeStatuses.delete(o.key);
                    } else {
                        btn.classList.add('active');
                        activeStatuses.add(o.key);
                    }
                    applyStatusFilter();
                });
                statusFilterContainer.appendChild(btn);
            });
        }

        function applyStatusFilter(){
            const rows = contentDiv.querySelectorAll('tr[data-status]');
            rows.forEach(row=>{
                const st = row.dataset.status;
                row.style.display = activeStatuses.has(st)? '' : 'none';
            });

            // Hide or show entire tables (and their titles) based on visible rows
            const tables = contentDiv.querySelectorAll('table.report-table');
            tables.forEach(table => {
                const hasVisibleRow = Array.from(table.querySelectorAll('tbody tr')).some(r => r.style.display !== 'none');
                const titleEl = table.previousElementSibling;
                if (hasVisibleRow) {
                    table.style.display = '';
                    if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = '';
                } else {
                    table.style.display = 'none';
                    if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = 'none';
                }
            });
        }

        // -----------------------------------------------------

        // ---------------- CSV Export -----------------------
        function escapeCsvValue(val){
            if (val == null) return '';
            const str = String(val).replace(/\s+/g,' ').trim();
            return /[",\n]/.test(str) ? `"${str.replace(/"/g,'""')}"` : str;
        }

        function exportCsv(){
            // Ensure there is content
            if (!contentDiv.querySelector('table')){
                alert('Nothing to export');
                return;
            }

            const type = typeSel.value;
            const rowsOut = [];

            if (type === 'services'){
                // Header
                rowsOut.push(['Host', 'Service', 'OK %', 'Warn %', 'Crit %', 'Unk %']);
            } else {
                rowsOut.push(['Hostgroup', 'Host', 'Up %', 'Down %', 'Unreachable %']);
            }

            // Collect visible rows
            const tables = contentDiv.querySelectorAll('table.report-table');
            tables.forEach(table=>{
                if (table.style.display === 'none') return; // hidden table
                const titleEl = table.previousElementSibling;
                const sectionName = titleEl?.textContent || '';

                Array.from(table.querySelectorAll('tbody tr')).forEach(tr=>{
                    if (tr.style.display === 'none') return; // filtered
                    const cells = Array.from(tr.cells).map(td=>td.textContent.trim());
                    if (type==='services'){
                        rowsOut.push([sectionName, ...cells]);
                    } else {
                        rowsOut.push([sectionName, ...cells]);
                    }
                });
            });

            if (rowsOut.length<=1){
                alert('No visible rows to export');
                return;
            }

            // Build CSV string
            const csvContent = rowsOut.map(r=>r.map(escapeCsvValue).join(',')).join('\n');

            // Download
            const blob = new Blob([csvContent], {type:'text/csv;charset=utf-8;'});
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            const ts = new Date().toISOString().replace(/[:T]/g,'-').split('.')[0];
            link.href = url;
            link.download = `report_${type}_${ts}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }




        // -----------------------------------------------------

        // ---------------- Save Report to Server -----------------------
        async function saveReport() {
            // Ensure there is content to save
            if (!contentDiv.querySelector('table')) {
                alert('Nothing to save');
                return;
            }

            const type = typeSel.value;
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                alert('Invalid time range');
                return;
            }

            // Show loading state
            const originalContent = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving...';

            try {
                const params = new URLSearchParams();
                params.append('type', type);
                params.append('startTs', startTs);
                params.append('endTs', endTs);
                params.append('hostStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
                params.append('svcStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
                params.append('hostgroup', hostgroupSel.value);

                const response = await fetch('functions/reportsFunctions/saveReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('Report saved successfully!');
                    // Refresh saved reports list if it exists
                    if (typeof loadSavedReports === 'function') {
                        loadSavedReports();
                    }
                } else {
                    alert('Failed to save report: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Save report error:', error);
                alert('Error saving report');
            } finally {
                // Restore button state
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalContent;
            }
        }

        // ---------------- Saved Reports Management -----------------------
        let savedReports = [];

        async function loadSavedReports() {
            try {
                const response = await fetch('functions/reportsFunctions/listSavedReports.php');
                const result = await response.json();
                
                if (result.success) {
                    savedReports = result.reports || [];
                    displaySavedReports();
                }
            } catch (error) {
                console.error('Failed to load saved reports:', error);
            }
        }

        function displaySavedReports() {
            const savedReportsContainer = document.getElementById('saved-reports-container');
            if (!savedReportsContainer) return;

            if (savedReports.length === 0) {
                savedReportsContainer.innerHTML = '<div class="saved-reports-empty">No saved reports found</div>';
                return;
            }

            let html = '<div class="saved-reports-list">';
            savedReports.forEach(report => {
                const date = new Date(report.created * 1000);
                const dateStr = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                
                html += `<div class="saved-report-item">
                    <div class="report-info">
                        <div class="report-title">${report.type === 'hostgroups' ? 'Hosts' : 'Services'} Report</div>
                        <div class="report-date">${dateStr}</div>
                        <div class="report-details">
                            ${report.rangeDays} day(s) | ${report.fileSizeFormatted}
                            ${report.scheduled ? ' | Scheduled' : ''}
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="report-action-btn view-btn" onclick="viewReport('${report.filename}')" title="View">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button class="report-action-btn download-btn" onclick="downloadReport('${report.filename}')" title="Download">
                            <i class="fa fa-download"></i>
                        </button>
                        <button class="report-action-btn delete-btn" onclick="deleteReport('${report.filename}')" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>`;
            });
            html += '</div>';
            savedReportsContainer.innerHTML = html;
        }

        // Global functions for report actions
        window.viewReport = function(filename) {
            window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=view', '_blank');
        };

        window.downloadReport = function(filename) {
            window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=download', '_blank');
        };

        window.deleteReport = async function(filename) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch('functions/reportsFunctions/deleteReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'file=' + encodeURIComponent(filename)
                });

                const result = await response.json();
                
                if (result.success) {
                    // Refresh the list
                    loadSavedReports();
                    // Show success message
                    alert('Report deleted successfully!');
                } else {
                    alert('Failed to delete report: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Delete report error:', error);
                alert('Error deleting report');
            }
        };

        // Load saved reports on page load
        loadSavedReports();

        // Function to open saved reports modal
        function openSavedReportsModal() {
            const savedReportsModal = document.getElementById('savedReportsModal');
            if (!savedReportsModal) return;
            
            savedReportsModal.style.display = 'flex';
            // Refresh the saved reports list when opening
            loadSavedReports();
        }

        const scheduleModal = document.getElementById('scheduleReportModal');
        const savedReportsModal = document.getElementById('savedReportsModal');
        const srClose = document.querySelectorAll('.sr-close');
        const srCancel = document.getElementById('sr-cancel');
        const srForm   = document.getElementById('schedule-form');
        const srSendNow = document.getElementById('sr-send-now');

        // Close modal handlers
        srClose.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const modal = closeBtn.closest('.sr-modal');
                if (modal) modal.style.display = 'none';
            });
        });
        
        // Click outside modal to close
        document.addEventListener('click', (event) => {
            const modals = document.querySelectorAll('.sr-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        if(srCancel){srCancel.addEventListener('click', ()=>{scheduleModal.style.display='none';});}
        
        // Send Now button handler
        if(srSendNow){
            srSendNow.addEventListener('click', function(){
                const email = document.getElementById('sr-email').value.trim();
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const sendNowBtn = document.getElementById('sr-send-now');
                sendNowBtn.disabled = true;
                sendNowBtn.textContent = 'Sending...';

                fetch('functions/reportsFunctions/sendInstantReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = js.message || 'Report sent successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to send report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Send now error', err);
                      if(srStatus){
                          srStatus.textContent = 'Error sending report';
                          srStatus.classList.remove('scheduled');
                          srStatus.classList.add('not-scheduled');
                      }
                  })
                  .finally(()=>{
                      sendNowBtn.disabled = false;
                      sendNowBtn.textContent = 'Send Now';
                  });
            });
        }
        if(srForm){
            srForm.addEventListener('submit', function(e){
                e.preventDefault();
                const email = document.getElementById('sr-email').value.trim();
                const freq  = document.getElementById('sr-frequency').value;
                const time  = document.getElementById('sr-time').value || '00:05';
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('frequency', freq);
                params.append('time', time);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const saveBtn = document.getElementById('sr-save');
                saveBtn.disabled = true;
                saveBtn.textContent = 'Saving...';

                fetch('functions/reportsFunctions/addReportCron.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = 'Report scheduled successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to schedule report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Schedule error', err);
                      alert('Error scheduling report');
                  })
                  .finally(()=>{
                      saveBtn.disabled = false;
                      saveBtn.textContent = 'Save';
                  });
            });
        }
        // Disable scheduled report handler
        if(srDisableBtn){
            srDisableBtn.addEventListener('click', ()=>{
                srDisableBtn.disabled = true;
                srDisableBtn.textContent = 'Disabling...';
                fetch('functions/reportsFunctions/deleteReportCron.php', { method: 'POST' })
                    .then(r=>r.json())
                    .then(js=>{
                        if(js.success){
                            srDisableBtn.style.display = 'none';
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = js.message || 'Failed to disable scheduled report';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.error('Disable schedule error', err);
                        if(srStatus){
                            srStatus.textContent = 'Error disabling schedule';
                            srStatus.classList.remove('scheduled');
                            srStatus.classList.add('not-scheduled');
                        }
                    })
                    .finally(()=>{
                        srDisableBtn.disabled = false;
                        srDisableBtn.textContent = 'Disable';
                    });
            });
        }
    });
})();
