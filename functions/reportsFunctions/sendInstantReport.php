<?php
// sendInstantReport.php - handles instant report sending via POST request
// Expected POST params: email, range, hostStatuses, svcStatuses

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Load environment variables (DB/Nagios credentials)
require_once dirname(__DIR__, 2) . '/loadenv.php';

// ------------------------- Helper functions (shared with sendScheduledReport.php) -------------------------------
/**
 * Get DB connection to 'blesk' for retrieving user credentials
 */
function getDatabaseConnectionAdminUser(): mysqli {
    $conn = new mysqli($_ENV['DB_SERVER'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], 'blesk');
    if ($conn->connect_error) {
        die('DB connection failed: ' . $conn->connect_error);
    }
    return $conn;
}

/**
 * Fetch Nagios HTTP basic auth credentials (user_id=1)
 */
function getUserCredentials(): ?array {
    $conn = getDatabaseConnectionAdminUser();
    $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
    $cred = null;
    if ($result && $row = $result->fetch_assoc()) {
        $cred = ['user' => $row['username'], 'pass' => $row['password']];
    }
    $conn->close();
    return $cred;
}

/**
 * Determine self IP address (stored by bubblemaps)
 */
function getSelfIp(): string {
    $ip = trim(@file_get_contents('/etc/sysconfig/ipaddr'));
    if (!$ip) die("Unable to determine self IP");
    return $ip;
}

// -----------------------------------------------------------------------------
// Configuration / constants
// -----------------------------------------------------------------------------
$SENDMAIL_PATH = '/usr/sbin/sendmail';
$FPDF_PATH     = __DIR__ . '/fpdf.php'; // optional FPDF library path
$NAGIOS_BASE   = 'https://' . getSelfIp(); // Use real host/IP instead of localhost
$TMP_DIR       = sys_get_temp_dir();
$HOSTGROUP_URL = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups';
$SERVICE_URL   = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services';

// -----------------------------------------------------------------------------
// Get POST parameters
// -----------------------------------------------------------------------------
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$rangeDays = isset($_POST['range']) ? max(1,min(365,intval($_POST['range']))) : 1;
$hostStatusesStr = strtolower(trim($_POST['hostStatuses'] ?? 'up,down,unreachable', "'\""));
$svcStatusesStr  = strtolower(trim($_POST['svcStatuses'] ?? 'ok,warning,critical,unknown', "'\""));
$hostStatuses = array_filter(explode(',', $hostStatusesStr));
$svcStatuses  = array_filter(explode(',', $svcStatusesStr));
$saveToServer = isset($_POST['saveToServer']) ? ($_POST['saveToServer'] === 'true') : false;

// Validate multiple emails separated by semicolons
$recipients = [];
if (!empty($email)) {
    $emails = array_map('trim', explode(';', $email));
    foreach ($emails as $singleEmail) {
        if (!empty($singleEmail)) {
            if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
                echo json_encode(['success' => false, 'message' => 'Invalid email address: ' . $singleEmail]);
                exit;
            }
            $recipients[] = $singleEmail;
        }
    }
}

if (empty($recipients)) {
    echo json_encode(['success' => false, 'message' => 'At least one valid email address must be provided.']);
    exit;
}

$sender = trim(@file_get_contents('/var/lib/blesk/emailsender')) ?: 'blesk@localhost';

// -----------------------------------------------------------------------------
// Helper to fetch Nagios availability data (same as sendScheduledReport.php)
// -----------------------------------------------------------------------------
function fetchJson(string $path, int $startTs, int $endTs): array
{
    $url = $GLOBALS['NAGIOS_BASE'] . $path . '&starttime=' . $startTs . '&endtime=' . $endTs;

    static $creds = null;
    if ($creds === null) $creds = getUserCredentials();

    // Initialise cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // Allow self-signed certificates (internal Nagios)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Pass basic-auth credentials if available
    if ($creds) {
        curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $raw = curl_exec($ch);
    if ($raw === false) {
        error_log('[sendInstantReport] cURL error: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }

    $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http !== 200) {
        error_log('[sendInstantReport] HTTP ' . $http . ' fetching ' . $url);
        return [];
    }

    $json = json_decode($raw, true);
    if (!is_array($json)) return [];
    return $json['data'] ?? [];
}

$END_TS = time();
$START_TS = $END_TS - ($rangeDays * 86400);

// -----------------------------------------------------------------------------
// Generate PDF report (same logic as sendScheduledReport.php)
// -----------------------------------------------------------------------------
$tmpPdf = $TMP_DIR . '/blesk_instant_report_' . date('Ymd_His') . '.pdf';

$fpdfAvailable = false;
if (!class_exists('FPDF') && file_exists($FPDF_PATH)) {
    require_once $FPDF_PATH;
}
if (class_exists('FPDF')) {
    $fpdfAvailable = true;
}

// After FPDF is available, define subclass for header/footer
if ($fpdfAvailable && !class_exists('BleskPDF')) {
    class BleskPDF extends FPDF {
        public string $titleText = '';
        public string $rangeText = '';
        public string $statusText = '';
        public string $logoPath = '';
        function Header() {
            // Logo (optional)
            if ($this->logoPath && file_exists($this->logoPath)) {
                $this->Image($this->logoPath, 10, 6, 12);
            }
            // Title
            $this->SetFont('Arial', 'B', 14);
            $this->Cell(0, 7, $this->titleText, 0, 1, 'C');
            // Date range
            $this->SetFont('Arial', '', 10);
            $this->Cell(0, 6, $this->rangeText, 0, 1, 'C');
            // Status filters
            if ($this->statusText) {
                $this->SetFont('Arial', '', 9);
                $this->Cell(0, 6, $this->statusText, 0, 1, 'C');
            }
            $this->Ln(3);
        }
        function Footer() {
            $this->SetY(-12);
            $this->SetFont('Arial', 'I', 8);
            $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
        }

        // Public helper to get printable width between margins
        public function ContentWidth(): float {
            return $this->GetPageWidth() - $this->lMargin - $this->rMargin;
        }
    }
}

if ($fpdfAvailable) {
    $titleRangeTxt = $rangeDays === 1 ? 'Last 24 Hours' : ('Last ' . $rangeDays . ' Days');
    $pdf = new BleskPDF('L', 'mm', 'A4');
    $pdf->AliasNbPages();
    $pdf->SetAuthor('Blesk');
    // Header content
    $pdf->titleText  = 'Blesk Report (' . $titleRangeTxt . ')';
    $pdf->rangeText  = 'Range: ' . date('Y-m-d H:i', $START_TS) . ' - ' . date('Y-m-d H:i', $END_TS);
    $pdf->statusText = 'Host statuses: ' . implode(', ', $hostStatuses) . ' | Service statuses: ' . implode(', ', $svcStatuses);
    // Try PNG logo (FPDF supports PNG/JPG)
    $logoPng = __DIR__ . '/blesk-logo-black_ret.png';
    if (file_exists($logoPng)) {
        $pdf->logoPath = $logoPng;
    }
    $pdf->SetTitle($pdf->titleText);
    $pdf->AddPage();

    // Common helpers ------------------------------------------------------
    $pdf->SetAutoPageBreak(true, 15);

    // Store TOC items and page references
    $tocItems = [];
    $pageRefs = [];

    // Generate Table of Contents with page numbers
    $generateTOC = function() use ($pdf, &$tocItems, &$pageRefs) {
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->Cell(0, 10, 'Table of Contents', 0, 1, 'L');
        $pdf->Ln(5);

        foreach ($tocItems as $item) {
            $pageNum = $pageRefs[$item['id']] ?? 1;

            if ($item['level'] == 1) {
                $pdf->SetFont('Arial', 'B', 12);
                $pdf->SetTextColor(0, 0, 0);

                // Create entry with page number
                $title = $item['title'];
                $pageText = "Page $pageNum";
                $titleWidth = $pdf->GetStringWidth($title);
                $pageWidth = $pdf->GetStringWidth($pageText);
                $availableWidth = $pdf->GetPageWidth() - 40; // 20mm margins on each side
                $dotsWidth = $availableWidth - $titleWidth - $pageWidth - 4; // 4mm padding
                $dots = str_repeat('.', max(1, floor($dotsWidth / $pdf->GetStringWidth('.'))));

                $pdf->Cell($titleWidth, 8, $title, 0, 0, 'L');
                $pdf->Cell($dotsWidth, 8, $dots, 0, 0, 'C');
                $pdf->Cell($pageWidth, 8, $pageText, 0, 1, 'R');

            } else {
                $pdf->SetFont('Arial', '', 10);
                $pdf->SetTextColor(0, 0, 0);

                $pdf->Cell(10, 6, '', 0, 0); // indent
                $title = '• ' . $item['title'];
                $pageText = "Page $pageNum";
                $titleWidth = $pdf->GetStringWidth($title);
                $pageWidth = $pdf->GetStringWidth($pageText);
                $availableWidth = $pdf->GetPageWidth() - 50; // 20mm margins + 10mm indent
                $dotsWidth = $availableWidth - $titleWidth - $pageWidth - 4;
                $dots = str_repeat('.', max(1, floor($dotsWidth / $pdf->GetStringWidth('.'))));

                $pdf->Cell($titleWidth, 6, $title, 0, 0, 'L');
                $pdf->Cell($dotsWidth, 6, $dots, 0, 0, 'C');
                $pdf->Cell($pageWidth, 6, $pageText, 0, 1, 'R');
            }
        }

        $pdf->Ln(10);
    };

    // Function to add TOC item and store page reference
    $addTOCItem = function($id, $title, $level) use (&$tocItems, &$pageRefs, $pdf) {
        $tocItems[] = ['id' => $id, 'title' => $title, 'level' => $level];
        $pageRefs[$id] = $pdf->PageNo();
    };

    // Fetch data first for TOC generation
    $hostData = fetchJson($HOSTGROUP_URL, $START_TS, $END_TS);
    $svcData = fetchJson($SERVICE_URL, $START_TS, $END_TS);

    // First pass: collect TOC items by going through the data
    $hostgroups = $hostData['hostgroups'] ?? ($hostData['hostgroup'] ? [$hostData['hostgroup']] : []);
    $services = $svcData['services'] ?? [];

    // Add main sections to TOC
    $addTOCItem('hosts-section', 'Hosts Section', 1);
    foreach ($hostgroups as $grp) {
        $allHosts = $grp['hosts'] ?? [];
        $filteredHosts = array_filter($allHosts, function($h) use ($hostStatuses){
            $status = ($h['time_down']>0)?'down':(($h['time_unreachable']>0)?'unreachable':'up');
            return in_array($status, $hostStatuses, true);
        });
        if(!empty($filteredHosts)) {
            $addTOCItem('hostgroup-' . ($grp['name'] ?? 'unnamed'), $grp['name'] ?? 'Unnamed Hostgroup', 2);
        }
    }

    $addTOCItem('services-section', 'Services Section', 1);
    if (!empty($services)) {
        $byHost = [];
        foreach ($services as $svc) {
            $svcStatus = ($svc['time_critical']>0)?'critical':(($svc['time_warning']>0)?'warning':(($svc['time_unknown']>0)?'unknown':'ok'));
            if(in_array($svcStatus,$svcStatuses, true)) {
                $byHost[$svc['host_name']] = true;
            }
        }
        foreach (array_keys($byHost) as $host) {
            $addTOCItem('host-' . $host, $host, 2);
        }
    }

    // Generate TOC
    $generateTOC();

    /**
     * Draw a light grey section title spanning full width with anchor
     */
    $drawSectionTitle = function(string $title, string $anchor = '') use ($pdf, &$pageRefs) {
        if ($anchor) {
            $pageRefs[$anchor] = $pdf->PageNo();
        }
        $pdf->SetFont('Arial', 'B', 12);
        $pdf->SetFillColor(230, 230, 230);
        $pdf->Cell(0, 8, utf8_decode($title), 0, 1, 'L', true);
        $pdf->Ln(2);
    };

    /**
     * Draw a table header row with uniform style
     */
    $drawTableHeader = function(array $headers, array $widths) use ($pdf) {
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->SetFillColor(245, 245, 245);
        foreach ($headers as $idx => $hdr) {
            $pdf->Cell($widths[$idx], 7, $hdr, 1, 0, 'C', true);
        }
        $pdf->Ln();
    };

    /**
     * Draw a numeric percentage cell using fill colours similar to jsPDF
     */
    $pctCell = function(float $value, float $width, string $kind) use ($pdf) {
        $fill = false;
        if ($value > 0) {
            switch ($kind) {
                case 'ok':
                case 'up':
                    $pdf->SetFillColor(165, 214, 167); // green
                    break;
                case 'warn':
                case 'warning':
                    $pdf->SetFillColor(255, 224, 130); // yellow
                    break;
                case 'crit':
                case 'down':
                case 'critical':
                    $pdf->SetFillColor(229, 115, 115); // red
                    break;
                default: // unknown / unreachable
                    $pdf->SetFillColor(169, 182, 201); // grey
            }
            $fill = true;
            $pdf->SetTextColor(51, 51, 51);
        }
        $pdf->Cell($width, 6, number_format($value, 1), 1, 0, 'R', $fill);
        if ($fill) $pdf->SetTextColor(0); // reset
    };

    // --------------------------------------------------------------------
    // Hosts Availability (Hostgroups) – mimic jsPDF consolidated layout
    // --------------------------------------------------------------------
    $hostgroups = $hostData['hostgroups'] ?? ($hostData['hostgroup'] ? [$hostData['hostgroup']] : []);

    $drawSectionTitle('Hosts Section', 'hosts-section');

    if (!empty($hostgroups)) {
        foreach ($hostgroups as $grp) {
            $allHosts = $grp['hosts'] ?? [];
            $filteredHosts = array_filter($allHosts, function($h) use ($hostStatuses){
                $status = ($h['time_down']>0)?'down':(($h['time_unreachable']>0)?'unreachable':'up');
                return in_array($status, $hostStatuses, true);
            });
            if(empty($filteredHosts)) continue;

            // Section (hostgroup) title with anchor
            $pageRefs['hostgroup-' . ($grp['name'] ?? 'unnamed')] = $pdf->PageNo();
            $pdf->SetFont('Arial', 'B', 10);
            $pdf->SetFillColor(245, 245, 245);
            $pageWidth = $pdf->ContentWidth();
            $pdf->Cell($pageWidth, 7, utf8_decode($grp['name'] ?? 'Unnamed Hostgroup'), 0, 1, 'L', true);

            // Table headers
            $hdrs = ['Host', 'Up %', 'Down %', 'Unreach %'];
            $pageWidth = $pdf->ContentWidth();
            $pctW = 25; // fixed width for percentage columns
            $widths = [$pageWidth - 3 * $pctW, $pctW, $pctW, $pctW];
            $drawTableHeader($hdrs, $widths);

            // Rows
            $pdf->SetFont('Arial', '', 8);
            foreach ($filteredHosts as $h) {
                $total = ($h['time_up'] + $h['time_down'] + $h['time_unreachable']);
                $upPct  = percent($h['time_up'], $total);
                $downPct = percent($h['time_down'], $total);
                $unPct  = percent($h['time_unreachable'], $total);

                $pdf->Cell($widths[0], 6, $h['name'] ?? '-', 1);
                $pctCell($upPct,   $widths[1], 'up');
                $pctCell($downPct, $widths[2], 'down');
                $pctCell($unPct,   $widths[3], 'unknown');
                $pdf->Ln();
            }
            $pdf->Ln(2);
        }
    } else {
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 6, 'No host data available', 0, 1);
    }

    // --------------------------------------------------------------------
    // Services Availability – mimic jsPDF consolidated layout
    // --------------------------------------------------------------------
    $services = $svcData['services'] ?? [];

    $drawSectionTitle('Services Section', 'services-section');

    if (!empty($services)) {
        // Group by host like in JS implementation, with status filtering
        $byHost = [];
        foreach ($services as $svc) {
            $svcStatus = ($svc['time_critical']>0)?'critical':(($svc['time_warning']>0)?'warning':(($svc['time_unknown']>0)?'unknown':'ok'));
            if(!in_array($svcStatus,$svcStatuses, true)) continue;
            $byHost[$svc['host_name']][] = $svc;
        }

        foreach ($byHost as $host => $svcList) {
            if(empty($svcList)) continue;
            // Host title row with anchor
            $pageRefs['host-' . $host] = $pdf->PageNo();
            $pdf->SetFont('Arial', 'B', 10);
            $pdf->SetFillColor(245, 245, 245);
            $pageWidth = $pdf->ContentWidth();
            $pdf->Cell($pageWidth, 7, utf8_decode($host), 0, 1, 'L', true);

            // Table headers
            $hdrsSvc = ['Service', 'OK %', 'Warn %', 'Crit %', 'Unk %'];
            $pageWidth = $pdf->ContentWidth();
            $pctW = 25;
            $wSvc  = [$pageWidth - 4 * $pctW, $pctW, $pctW, $pctW, $pctW];
            $drawTableHeader($hdrsSvc, $wSvc);

            $pdf->SetFont('Arial', '', 8);
            foreach ($svcList as $svc) {
                $total = ($svc['time_ok'] + $svc['time_warning'] + $svc['time_critical'] + $svc['time_unknown']);
                $okPct   = percent($svc['time_ok'], $total);
                $warnPct = percent($svc['time_warning'], $total);
                $critPct = percent($svc['time_critical'], $total);
                $unkPct  = percent($svc['time_unknown'], $total);

                $pdf->Cell($wSvc[0], 6, $svc['description'] ?? '-', 1);
                $pctCell($okPct,   $wSvc[1], 'ok');
                $pctCell($warnPct, $wSvc[2], 'warn');
                $pctCell($critPct, $wSvc[3], 'crit');
                $pctCell($unkPct,  $wSvc[4], 'unknown');
                $pdf->Ln();
            }
            $pdf->Ln(2);
        }
    } else {
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 6, 'No service data available', 0, 1);
    }

    $pdf->Output('F', $tmpPdf);
} else {
    // FPDF unavailable -> create very small placeholder PDF
    file_put_contents($tmpPdf, "%PDF-1.4\n1 0 obj<<>>endobj\ntrailer<<>>\n%%EOF");
}

// -----------------------------------------------------------------------------
// Compose and send the email with attachment
// -----------------------------------------------------------------------------
$subject   = 'Blesk Report (Last '.$rangeDays.($rangeDays===1?' Day':' Days').') - ' . date('Y-m-d');
// ---------------- Email MIME building with plain+HTML and attachment ---------------
$boundary  = '==Mixed_' . md5(time()) . 'x';
$altBoundary = '==Alt_' . md5(time() + 1) . 'x';

$headers   = 'From: ' . $sender . "\r\n";
$headers  .= 'MIME-Version: 1.0' . "\r\n";
$headers  .= 'Content-Type: multipart/mixed; boundary="' . $boundary . '"' . "\r\n";

$htmlBody = '<p>Please find attached the Blesk availability report for the last ' . $rangeDays . ($rangeDays===1?' day':' days') . '.</p>';

$body  = '--' . $boundary . "\r\n";
$body .= 'Content-Type: multipart/alternative; boundary="' . $altBoundary . '"' . "\r\n\r\n";

// Plain text part
$body .= '--' . $altBoundary . "\r\n";
$body .= 'Content-Type: text/plain; charset="UTF-8"' . "\r\n";
$body .= 'Content-Transfer-Encoding: 7bit' . "\r\n\r\n";
$body .= "Please find attached the Blesk availability report for the last " . $rangeDays . ($rangeDays===1?' day':' days') . ".\r\n\r\n";

// HTML part
$body .= '--' . $altBoundary . "\r\n";
$body .= 'Content-Type: text/html; charset="UTF-8"' . "\r\n";
$body .= 'Content-Transfer-Encoding: 7bit' . "\r\n\r\n";
$body .= $htmlBody . "\r\n\r\n";

$body .= '--' . $altBoundary . '--' . "\r\n";

// Attach PDF
$fileContent = chunk_split(base64_encode(file_get_contents($tmpPdf)));
$body .= '--' . $boundary . "\r\n";
$body .= 'Content-Type: application/pdf; name="blesk_report.pdf"' . "\r\n";
$body .= 'Content-Disposition: attachment; filename="blesk_report.pdf"' . "\r\n";
$body .= 'Content-Transfer-Encoding: base64' . "\r\n\r\n";
$body .= $fileContent . "\r\n";
$body .= '--' . $boundary . '--';

// Send using sendmail (better reliability than PHP mail())
$cmd = escapeshellcmd($SENDMAIL_PATH) . ' -t -f ' . escapeshellarg($sender);
$proc = popen($cmd, 'w');
if ($proc === false) {
    fwrite(STDERR, "Failed to invoke sendmail.\n");
    unlink($tmpPdf);
    echo json_encode(['success' => false, 'message' => 'Failed to invoke sendmail.']);
    exit;
}

// Send to all recipients
fwrite($proc, 'To: ' . implode(', ', $recipients) . "\n");
fwrite($proc, 'Subject: ' . $subject . "\n");
foreach (explode("\r\n", trim($headers)) as $hdrLine) {
    fwrite($proc, $hdrLine . "\n");
}

fwrite($proc, "\n" . $body);
$pcloseStatus = pclose($proc);

// Save a copy to the reports directory if requested
if ($saveToServer) {
    $reportsDir = __DIR__ . '/saved_reports';
    if (is_dir($reportsDir)) {
        $savedFilename = 'blesk_report_instant_' . date('Ymd_His') . '.pdf';
        $savedPath = $reportsDir . '/' . $savedFilename;
        
        if (copy($tmpPdf, $savedPath)) {
            // Save metadata
            $metadata = [
                'filename' => $savedFilename,
                'type' => 'instant',
                'startTs' => $START_TS,
                'endTs' => $END_TS,
                'hostStatuses' => $hostStatuses,
                'svcStatuses' => $svcStatuses,
                'hostgroup' => '',
                'email' => implode(';', $recipients),
                'created' => time(),
                'createdDate' => date('Y-m-d H:i:s'),
                'rangeDays' => $rangeDays,
                'scheduled' => false
            ];
            
            $metadataFile = $reportsDir . '/' . pathinfo($savedFilename, PATHINFO_FILENAME) . '.json';
            file_put_contents($metadataFile, json_encode($metadata, JSON_PRETTY_PRINT));
        }
    }
}

// Clean up temporary file
unlink($tmpPdf);

if ($pcloseStatus === 0) {
    echo json_encode(['success' => true, 'message' => 'Report sent successfully to ' . implode(', ', $recipients)]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to send report (exit ' . $pcloseStatus . ').']);
}

// ------------------------ Helper ----------------------------
function percent($part, $total)
{
    if ($total == 0) return 0.0;
    return ($part / $total) * 100.0;
}
?> 