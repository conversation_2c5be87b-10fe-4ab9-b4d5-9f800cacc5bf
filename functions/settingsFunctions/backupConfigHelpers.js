/**
 * Backup Configuration Helpers
 * Handles backup operations in the Settings modal
 */
$(document).ready(function() {
    // Cache DOM elements
    const $backupConfigForm = $('#backup-config-form');
    const $backupTabs = $('.backup-tab-link[data-target^="backup-"]');
    const $backupTabContents = $('.backup-tab-content[id^="backup-"]');
    const $backupTypeRadios = $('input[name="backup_type"]');
    const $backupModuleCheckboxes = $('input[name="backup_module"]');
    const $startBackupBtn = $('#start-backup-btn');
    const $backupStatus = $('#backup-status');
    const $refreshBackupsBtn = $('#refresh-backups-btn');
    const $backupFilesContainer = $('#backup-files-container');
    const $backupFilesLoading = $('#backup-files-loading');
    const $backupFilesTableContainer = $('#backup-files-table-container');
    const $backupFilesTableBody = $('#backup-files-table-body');
    const $noBackupFiles = $('#no-backup-files');
    
    // SCP Settings Elements
    const $scpSettingsContainer = $('#scp-settings-container');
    const $scpServer = $('#scp_server');
    const $scpUser = $('#scp_user');
    const $scpPassword = $('#scp_password');
    const $scpFolder = $('#scp_folder');
    const $scpDeleteLocal = $('#scp_delete_local');
    const $testScpBtn = $('#test-scp-btn');
    
    // FTP Settings Elements
    const $ftpSettingsContainer = $('#ftp-settings-container');
    const $ftpServer = $('#ftp_server');
    const $ftpUser = $('#ftp_user');
    const $ftpPassword = $('#ftp_password');
    const $ftpFolder = $('#ftp_folder');
    const $ftpDeleteLocal = $('#ftp_delete_local');
    const $testFtpBtn = $('#test-ftp-btn');
    
    // Password toggle element references
    const $toggleScpPassword = $('#toggle-scp-password');
    const $toggleFtpPassword = $('#toggle-ftp-password');
    
    // Auto schedule toggle
    const $autoScheduleToggle = $('#auto_schedule');
    const $autoFrequency = $('#auto_frequency');
    const $autoTime = $('#auto_time');
    const $autoScheduleOptions = $('#auto-schedule-options');
    
    // Initialize password toggles
    $toggleScpPassword.on('click', function() {
        const type = $scpPassword.attr('type');
        if (type === 'password') {
            $scpPassword.attr('type', 'text');
            $(this).removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            $scpPassword.attr('type', 'password');
            $(this).removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    $toggleFtpPassword.on('click', function() {
        const type = $ftpPassword.attr('type');
        if (type === 'password') {
            $ftpPassword.attr('type', 'text');
            $(this).removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            $ftpPassword.attr('type', 'password');
            $(this).removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Add auto-hide functionality
    function hidePassword($input, $toggle) {
        if ($input.attr('type') === 'text') {
            $input.attr('type', 'password');
            $toggle.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    }

    // Auto-hide password after 5 seconds
    $toggleScpPassword.on('click', function() {
        if ($scpPassword.attr('type') === 'text') {
            setTimeout(() => hidePassword($scpPassword, $toggleScpPassword), 5000);
        }
    });

    $toggleFtpPassword.on('click', function() {
        if ($ftpPassword.attr('type') === 'text') {
            setTimeout(() => hidePassword($ftpPassword, $toggleFtpPassword), 5000);
        }
    });
    
    // Backup in progress check timer
    let backupStatusTimer = null;
    
    // Set all module checkboxes to checked by default
    $backupModuleCheckboxes.prop('checked', true);
    
    // Initialize tab navigation for backup sections
    $backupTabs.on('click', function(e) {
        e.preventDefault();
        const targetTab = $(this).data('target');
        
        // Update active state for tab links
        $backupTabs.removeClass('active');
        $(this).addClass('active');
        
        // Show the selected tab content, hide others
        $backupTabContents.hide();
        $('#' + targetTab).show();
        
        // If switching to files tab, load backup files
        if (targetTab === 'backup-files-tab') {
            loadBackupFiles();
        }
    });
    
    // Load backup settings when the tab is activated
    $('.sub-tab-link[data-target="general-config-backup"]').on('click', function() {
        loadBackupSettings();
        
        // If backup files tab is active, load backup files
        if ($('.backup-tab-link[data-target="backup-files-tab"]').hasClass('active')) {
            loadBackupFiles();
        }
    });
    
    // Handle changes to backup type selection
    $backupTypeRadios.on('change', function() {
        saveBackupSettings();
        // Show/hide remote settings containers based on selection
        const selectedValue = $(this).val();
        if (selectedValue === 'scp') {
            $scpSettingsContainer.slideDown();
            $ftpSettingsContainer.slideUp();
        } else if (selectedValue === 'ftp') {
            $ftpSettingsContainer.slideDown();
            $scpSettingsContainer.slideUp();
        } else {
            $scpSettingsContainer.slideUp();
            $ftpSettingsContainer.slideUp();
        }
    });
    
    // Handle changes to SCP fields - auto-save when they change
    $scpServer.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $scpUser.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $scpPassword.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $scpFolder.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $scpDeleteLocal.on('change', function() {
        saveBackupSettings(false);
    });
    
    // Test SCP Connection
    $testScpBtn.on('click', function() {
        testConnection('scp');
    });
    
    // Handle changes to FTP fields - auto-save when they change
    $ftpServer.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $ftpUser.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $ftpPassword.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $ftpFolder.on('blur', function() {
        if ($(this).val() !== $(this).data('last-value')) {
            $(this).data('last-value', $(this).val());
            saveBackupSettings(false);
        }
    });
    
    $ftpDeleteLocal.on('change', function() {
        saveBackupSettings(false);
    });
    
    // Test FTP Connection
    $testFtpBtn.on('click', function() {
        testConnection('ftp');
    });
    
    // Handle changes to auto schedule controls
    function updateAutoScheduleVisibility() {
        if ($autoScheduleToggle.is(':checked')) {
            $autoScheduleOptions.show();
        } else {
            $autoScheduleOptions.hide();
        }
    }

    // Initial visibility
    updateAutoScheduleVisibility();

    $autoScheduleToggle.on('change', function() {
        updateAutoScheduleVisibility();
        saveBackupSettings(true);
    });

    $autoFrequency.on('change', function() {
        saveBackupSettings(true);
    });

    $autoTime.on('change', function() {
        saveBackupSettings(true);
    });
    
    // Handle changes to module checkboxes
    $backupModuleCheckboxes.on('change', function() {
        saveBackupSettings(false);
    });
    
    // Start backup button
    $startBackupBtn.on('click', function() {
        startBackup();
    });
    
    // Refresh backup files button
    $refreshBackupsBtn.on('click', function() {
        loadBackupFiles();
    });
    
    /**
     * Test connection to remote server (SCP or FTP)
     * @param {string} type Connection type ('scp' or 'ftp')
     */
    function testConnection(type) {
        let serverSettings = {};
        let $testBtn;
        
        if (type === 'scp') {
            // Validate SCP settings
            if (!$scpServer.val() || !$scpUser.val()) {
                showStatus('error', 'Please provide server address and username for SCP connection test');
                return;
            }
            
            serverSettings = {
                server: $scpServer.val(),
                user: $scpUser.val(),
                password: $scpPassword.val(),
                folder: $scpFolder.val()
            };
            $testBtn = $testScpBtn;
        } else if (type === 'ftp') {
            // Validate FTP settings
            if (!$ftpServer.val() || !$ftpUser.val()) {
                showStatus('error', 'Please provide server address and username for FTP connection test');
                return;
            }
            
            serverSettings = {
                server: $ftpServer.val(),
                user: $ftpUser.val(),
                password: $ftpPassword.val(),
                folder: $ftpFolder.val()
            };
            $testBtn = $testFtpBtn;
        } else {
            return; // Invalid type
        }
        
        // Update button state
        $testBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Testing...');
        
        // Call server to test connection
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: {
                action: 'testConnection',
                connection_type: type,
                settings: serverSettings
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showStatus('success', response.message || 'Connection test successful');
                } else {
                    showStatus('error', response.message || 'Connection test failed');
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Connection test failed: ' + error);
            },
            complete: function() {
                // Reset button state
                if (type === 'scp') {
                    $testBtn.prop('disabled', false).html('<i class="fa fa-plug"></i>&nbsp;Test SCP Connection');
                } else {
                    $testBtn.prop('disabled', false).html('<i class="fa fa-plug"></i>&nbsp;Test FTP Connection');
                }
            }
        });
    }
    
    /**
     * Load backup settings from the server
     */
    function loadBackupSettings() {
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: { action: 'getBackupModules' },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.modules) {
                    const modules = response.modules;
                    
                    // Set backup type
                    $backupTypeRadios.filter(`[value="${modules.backup_type}"]`).prop('checked', true);
                    
                    // Show/hide remote settings containers based on loaded backup type
                    if (modules.backup_type === 'scp') {
                        $scpSettingsContainer.show();
                        $ftpSettingsContainer.hide();
                    } else if (modules.backup_type === 'ftp') {
                        $ftpSettingsContainer.show();
                        $scpSettingsContainer.hide();
                    } else {
                        $scpSettingsContainer.hide();
                        $ftpSettingsContainer.hide();
                    }
                    
                    // Populate SCP fields if they exist in the response and store initial values
                    $scpServer.val(modules.scp_server || '').data('last-value', modules.scp_server || '');
                    $scpUser.val(modules.scp_user || '').data('last-value', modules.scp_user || '');
                    $scpPassword.val(modules.scp_password || '').data('last-value', modules.scp_password || '');
                    $scpFolder.val(modules.scp_folder || '').data('last-value', modules.scp_folder || '');
                    $scpDeleteLocal.prop('checked', modules.scp_delete_local === 'yes');
                    
                    // Populate FTP fields if they exist in the response and store initial values
                    $ftpServer.val(modules.ftp_server || '').data('last-value', modules.ftp_server || '');
                    $ftpUser.val(modules.ftp_user || '').data('last-value', modules.ftp_user || '');
                    $ftpPassword.val(modules.ftp_password || '').data('last-value', modules.ftp_password || '');
                    $ftpFolder.val(modules.ftp_folder || '').data('last-value', modules.ftp_folder || '');
                    $ftpDeleteLocal.prop('checked', modules.ftp_delete_local === 'yes');
                    
                    // Count how many modules are explicitly set to 'yes'
                    let enabledModulesCount = 0;
                    
                    // Set module checkboxes based on config
                    $backupModuleCheckboxes.each(function() {
                        const moduleName = $(this).val();
                        const isEnabled = modules[moduleName] === 'yes';
                        
                        if (isEnabled) {
                            enabledModulesCount++;
                        }
                        
                        $(this).prop('checked', isEnabled);
                    });
                    
                    // If no modules are set (fresh installation), check all modules by default
                    if (enabledModulesCount === 0) {
                        $backupModuleCheckboxes.prop('checked', true);
                        // Save this default state to server
                        saveBackupSettings(false);
                    }
                    
                    // Set auto schedule controls
                    $autoScheduleToggle.prop('checked', modules.auto_schedule === 'yes');
                    $autoFrequency.val(modules.auto_frequency || 'daily');
                    $autoTime.val(modules.auto_time || '02:00');
                    
                    updateAutoScheduleVisibility();
                    
                    // Check for backup in progress
                    checkBackupStatus();
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Failed to load backup settings: ' + error);
                
                // On error, ensure all checkboxes are checked by default
                $backupModuleCheckboxes.prop('checked', true);
            }
        });
    }
    
    /**
     * Save backup settings to the server
     * @param {boolean} showMessage If true, show a success message when settings are saved successfully
     */
    function saveBackupSettings(showMessage = false) {
        // Build modules object
        const modules = {
            backup_type: $backupTypeRadios.filter(':checked').val()
        };
        
        // Add module settings
        $backupModuleCheckboxes.each(function() {
            const moduleName = $(this).val();
            modules[moduleName] = $(this).prop('checked') ? 'yes' : 'no';
        });
        
        // Add auto schedule settings
        modules.auto_schedule = $autoScheduleToggle.is(':checked') ? 'yes' : 'no';
        modules.auto_frequency = $autoFrequency.val();
        modules.auto_time = $autoTime.val();
        
        // Add SCP settings if backup type is scp
        if (modules.backup_type === 'scp') {
            modules.scp_server = $scpServer.val();
            modules.scp_user = $scpUser.val();
            modules.scp_password = $scpPassword.val(); // Password will be sent
            modules.scp_folder = $scpFolder.val();
            modules.scp_delete_local = $scpDeleteLocal.is(':checked') ? 'yes' : 'no';
        }
        
        // Add FTP settings if backup type is ftp
        if (modules.backup_type === 'ftp') {
            modules.ftp_server = $ftpServer.val();
            modules.ftp_user = $ftpUser.val();
            modules.ftp_password = $ftpPassword.val(); // Password will be sent
            modules.ftp_folder = $ftpFolder.val();
            modules.ftp_delete_local = $ftpDeleteLocal.is(':checked') ? 'yes' : 'no';
        }
        
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: {
                action: 'saveBackupModules',
                modules: modules,
                silent: !showMessage // Invert the showMessage parameter for the server-side silent parameter
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    if (showMessage && response.message) {
                        showStatus('success', response.message);
                    }
                } else {
                    showStatus('error', response.message || 'Failed to save backup settings');
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Failed to save backup settings: ' + error);
            }
        });
    }
    
    /**
     * Start a backup operation
     */
    function startBackup() {
        // Check if any modules are selected
        const anySelected = $backupModuleCheckboxes.filter(':checked').length > 0;
        console.log('Checked modules count:', $backupModuleCheckboxes.filter(':checked').length);
        
        if (!anySelected) {
            showStatus('error', 'Please select at least one module to backup');
            return;
        }
        
        // Disable the button and show loading state
        $startBackupBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Starting Backup...');
        
        // Build modules object for the backup
        const modules = {};
        $backupModuleCheckboxes.each(function() {
            const moduleName = $(this).val();
            modules[moduleName] = $(this).prop('checked') ? 'yes' : 'no';
        });
        
        // Include backup type and remote settings if applicable for the startBackup call
        const backupType = $backupTypeRadios.filter(':checked').val();
        const dataToSend = {
            action: 'startBackup',
            backup_type: backupType,
            modules: modules
        };

        if (backupType === 'scp') {
            dataToSend.scp_settings = {
                server: $scpServer.val(),
                user: $scpUser.val(),
                password: $scpPassword.val(),
                folder: $scpFolder.val(),
                delete_local: $scpDeleteLocal.is(':checked') ? 'yes' : 'no'
            };
        } else if (backupType === 'ftp') {
            dataToSend.ftp_settings = {
                server: $ftpServer.val(),
                user: $ftpUser.val(),
                password: $ftpPassword.val(),
                folder: $ftpFolder.val(),
                delete_local: $ftpDeleteLocal.is(':checked') ? 'yes' : 'no'
            };
        }
        
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: dataToSend, // Use the modified dataToSend object
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showStatus('success', response.message);
                    
                    // Start checking backup status
                    checkBackupStatus();
                } else {
                    showStatus('error', response.message || 'Failed to start backup');
                    $startBackupBtn.prop('disabled', false).html('<i class="fa fa-play-circle"></i>&nbsp;Start Backup Now');
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Failed to start backup: ' + error);
                $startBackupBtn.prop('disabled', false).html('<i class="fa fa-play-circle"></i>&nbsp;Start Backup Now');
            }
        });
    }
    
    /**
     * Check if a backup is in progress
     */
    function checkBackupStatus() {
        // Clear any existing timer
        if (backupStatusTimer) {
            clearTimeout(backupStatusTimer);
            backupStatusTimer = null;
        }
        
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: { action: 'checkBackupStatus' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    if (response.inProgress) {
                        // Backup is in progress
                        $startBackupBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Backup in Progress...');
                        showStatus('info', 'A backup is currently in progress. Please wait...');
                        
                        // Check again in 5 seconds
                        backupStatusTimer = setTimeout(checkBackupStatus, 5000);
                    } else {
                        // Backup is not in progress
                        $startBackupBtn.prop('disabled', false).html('<i class="fa fa-play-circle"></i>&nbsp;Start Backup Now');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to check backup status:', error);
                
                // Enable button in case of error
                $startBackupBtn.prop('disabled', false).html('<i class="fa fa-play-circle"></i>&nbsp;Start Backup Now');
            }
        });
    }
    
    /**
     * Load backup files from the server
     */
    function loadBackupFiles() {
        $backupFilesLoading.show();
        $backupFilesTableContainer.hide();
        $noBackupFiles.hide();
        
        $.ajax({
            url: 'src/backupConfig/backupConfigHandler.php',
            type: 'POST',
            data: { action: 'getBackupFiles' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderBackupFiles(response.files);
                } else {
                    showStatus('error', 'Failed to load backup files');
                    $noBackupFiles.show();
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Failed to load backup files: ' + error);
                $noBackupFiles.show();
            },
            complete: function() {
                $backupFilesLoading.hide();
            }
        });
    }
    
    /**
     * Render backup files in the table
     * 
     * @param {Array} files List of backup files
     */
    function renderBackupFiles(files) {
        // Clear the table
        $backupFilesTableBody.empty();
        
        if (files && files.length > 0) {
            files.forEach(function(file) {
                // Format file size
                const fileSize = formatFileSize(file.size);
                
                // Create table row
                const $row = $('<tr>').css({
                    'transition': 'background-color 0.2s ease'
                }).hover(
                    function() { $(this).css('background-color', 'var(--background)'); },
                    function() { $(this).css('background-color', ''); }
                );
                
                // File name - styled for better readability
                $row.append($('<td>').css({
                    'padding': '15px',
                    'text-align': 'left',
                    'border-bottom': '1px solid var(--border)',
                    'font-family': 'monospace',
                    'word-break': 'break-all'
                }).text(file.name));
                
                // Date - formatted
                $row.append($('<td>').css({
                    'padding': '15px',
                    'text-align': 'left',
                    'border-bottom': '1px solid var(--border)'
                }).text(file.date));
                
                // Size - right aligned for better presentation
                $row.append($('<td>').css({
                    'padding': '15px',
                    'text-align': 'left',
                    'border-bottom': '1px solid var(--border)'
                }).text(fileSize));
                
                // Actions cell
                const $actionsCell = $('<td>').css({
                    'padding': '15px',
                    'text-align': 'center',
                    'border-bottom': '1px solid var(--border)'
                });
                
                // Common button styling function
                function applyButtonStyle(element) {
                    return element.css({
                        'display': 'inline-flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'width': '28px',
                        'min-width': '28px',
                        'height': '28px',
                        'padding': '0',
                        'margin-right': '5px',
                        'border-radius': '4px',
                        'transition': 'all 0.2s ease',
                        'font-size': '14px'
                    });
                }
                
                // Download button
                const $downloadBtn = $('<a>')
                    .attr('href', 'src/backupConfig/downloadBackup.php?file=' + encodeURIComponent(file.name))
                    .attr('title', 'Download')
                    .attr('download', file.name)
                    .addClass('btn-restart');
                
                applyButtonStyle($downloadBtn)
                    .append($('<i>').addClass('fa fa-download').css('margin', '0'));
                
                // Delete button
                const $deleteBtn = $('<button>')
                    .attr('type', 'button')
                    .attr('title', 'Delete')
                    .addClass('btn-stop')
                    .data('filename', file.name);
                
                applyButtonStyle($deleteBtn)
                    .append($('<i>').addClass('fa fa-trash').css('margin', '0'))
                    .on('click', function() {
                        deleteBackupFile($(this).data('filename'));
                    });
                
                $actionsCell.append($downloadBtn);
                $actionsCell.append($deleteBtn);
                $row.append($actionsCell);
                
                // Add row to table
                $backupFilesTableBody.append($row);
            });
            
            $backupFilesTableContainer.show();
            $noBackupFiles.hide();
        } else {
            $backupFilesTableContainer.hide();
            $noBackupFiles.show();
        }
    }
    
    /**
     * Format file size for display
     * 
     * @param {number} bytes File size in bytes
     * @returns {string} Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Show status message
     * 
     * @param {string} type Message type (success, error, info)
     * @param {string} message Message to display
     */
    function showStatus(type, message) {
        // Clear any existing timeout
        if ($backupStatus.data('timeout')) {
            clearTimeout($backupStatus.data('timeout'));
        }
        
        // Remove all classes and add the appropriate one
        $backupStatus.removeClass('message-success message-error message-info')
            .addClass('message-' + type);
        
        // Set icon based on type
        let icon = 'fa-info-circle';
        if (type === 'success') icon = 'fa-check-circle';
        if (type === 'error') icon = 'fa-exclamation-triangle';
        
        // Set content and show
        $backupStatus.html('<i class="fa ' + icon + '"></i>&nbsp;' + message).fadeIn();
        
        // Scroll to the message
        setTimeout(function() {
            const statusPosition = $backupStatus.offset().top;
            const modalScrollTop = $('.modal-content-area').scrollTop();
            const modalOffset = $('.modal-content-area').offset().top;
            const scrollTo = statusPosition - modalOffset + modalScrollTop - 20; // 20px padding
            
            $('.modal-content-area').animate({
                scrollTop: scrollTo
            }, 300);
        }, 100);
        
        // Auto-hide success and info messages after 5 seconds
        if (type !== 'error') {
            const timeout = setTimeout(function() {
                $backupStatus.fadeOut();
            }, 5000);
            
            $backupStatus.data('timeout', timeout);
        }
    }
    
    /**
     * Delete a backup file
     * 
     * @param {string} filename File to delete
     */
    function deleteBackupFile(filename) {
        if (!confirm('Are you sure you want to delete this backup file?\nThis action cannot be undone.')) {
            return;
        }
        
        $.ajax({
            url: 'src/backupConfig/deleteBackup.php',
            type: 'POST',
            data: { 
                filename: filename 
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    loadBackupFiles(); // Reload the list without showing a success message
                } else {
                    showStatus('error', response.message || 'Failed to delete backup file');
                }
            },
            error: function(xhr, status, error) {
                showStatus('error', 'Failed to delete backup file: ' + error);
            }
        });
    }
});