/* Reports Page - Light Theme */

.reports-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff;
    overflow: visible;
}

/* Controls bar */
.reports-controls {
    padding: 12px 18px;
    background-color: #403c3c;
    border-bottom: 1px solid #555;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.controls-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-group label {
    color: #ffffff;
    font-size: 14px;
    white-space: nowrap;
}

.control-group input[type="datetime-local"],
.control-group select {
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 6px 10px;
    font-size: 14px;
    height: 32px;
}

.generate-btn {
    background: transparent;
    border: 1px solid #777;
    color: #ccc;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.generate-btn:hover {
    background-color: #555;
    color: #fff;
}

/* Content */
.reports-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 18px 18px;
    background-color: #ffffff;
    margin-bottom: 20px;
    max-height: calc(100vh - 200px);
}

.reports-loading,
.reports-empty,
.reports-error,
.reports-placeholder {
    text-align: center;
    color: #777;
    font-size: 16px;
    padding: 40px 20px;
}

.reports-error {
    color: #d41d28;
}

/* Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.report-table thead {
    position: sticky;
    top: -1px;
    background-color: #f5f5f5;
    z-index: 5;
    padding-top: 1px;
}

.report-table th,
.report-table td {
    padding: 10px 14px;
    text-align: left;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
}

.report-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status colours */
.ok { color: #4CAF50; }
.warning { color: #FFC107; }
.critical { color: #F44336; }
.unknown { color: #64748b; }

/* Table of Contents */
.table-of-contents {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0 30px;
    max-width: 400px;
}

.toc-title {
    margin: 0 0 15px;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-list li {
    margin: 8px 0;
}

.toc-level-1 {
    font-weight: 600;
}

.toc-level-2 {
    margin-left: 20px;
    font-weight: 400;
}

.toc-link {
    color: #3498db;
    text-decoration: none;
    display: block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.toc-link:hover {
    background-color: #e3f2fd;
    text-decoration: none;
}

/* Main section title */
.report-main-title {
    margin: 30px 0 20px;
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* Hostgroup title */
.report-title {
    margin: 25px 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.report-title:first-of-type {
    margin-top: 0;
}

.reports-content > .report-title:first-child {
    margin-top: 0;
}

.report-table td.ok { background-color: rgba(76,175,80,0.12); }
.report-table td.critical { background-color: rgba(244,67,54,0.12); }
.report-table td.warning { background-color: rgba(255,193,7,0.12); }
.report-table td.unknown { background-color: rgba(100,116,139,0.12); }

.hostgroup-table { margin-bottom: 35px; }

/* Status filter buttons */
.status-row { justify-content: flex-start; }

.reports-status-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px 10px;
}

.reports-status-filter {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.reports-status-filter.ok { background:#4CAF50; color:#fff; }
.reports-status-filter.warning { background:#FFC107; color:#333; }
.reports-status-filter.critical { background:#F44336; color:#fff; }
.reports-status-filter.unknown { background:#64748b; color:#fff; }
.reports-status-filter.active { box-shadow: 0 0 0 2px #fff; opacity:1; }
.reports-status-filter:not(.active) { filter: grayscale(0.5) opacity(0.5); }

/* Scrollbar styles */
.reports-content {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #bbb #ffffff; /* thumb track */
}

.reports-content::-webkit-scrollbar {
    width: 8px;
}

.reports-content::-webkit-scrollbar-track {
    background: #ffffff;
}

.reports-content::-webkit-scrollbar-thumb {
    background-color: #bbb;
    border-radius: 4px;
}

.reports-content::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}

/* Schedule Report Modal */
.sr-modal{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;align-items:center;justify-content:center;}
.sr-modal-content{background:#fdfdfd;padding:25px 30px;border-radius:6px;min-width:320px;max-width:600px;color:#333;}
.sr-modal-content h2{margin-top:0;margin-bottom:15px;font-size:20px;}
.sr-close{float:right;font-size:28px;cursor:pointer;color:#666;}
.sr-close:hover{color:#000;}
.sr-field{display:flex;flex-direction:column;gap:6px;margin-bottom:12px;}
.sr-field label{font-size:14px;color:#333;}
.sr-field input, .sr-field select{background:#fff;border:1px solid #ccc;color:#333;padding:6px 10px;border-radius:4px;}
.sr-actions{display:flex;justify-content:flex-end;gap:10px;margin-top:10px;}

/* Schedule modal enhancements */
.sr-status{font-size:14px;margin-bottom:6px;}
.sr-status.scheduled{color:#4CAF50;font-weight:600;}
.sr-status.not-scheduled{color:#d41d28;font-weight:600;}

/* Buttons inside modal */
.sr-modal .generate-btn{background-color:#403c3c;border:none;color:#fff;}
.sr-modal .generate-btn:hover{background-color:#555;}
.sr-modal .generate-btn.red{background-color:#d9534f;}
.sr-modal .generate-btn.red:hover{background-color:#c9302c;}
.sr-modal .generate-btn:first-child{background-color:#28a745;}
.sr-modal .generate-btn:first-child:hover{background-color:#218838;}

/* Saved Reports Container (in modal) */
.saved-reports-container {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.saved-reports-loading,
.saved-reports-empty {
    text-align: center;
    color: #777;
    font-size: 14px;
    padding: 20px;
}

.saved-reports-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.saved-report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.saved-report-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.report-date {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.report-details {
    font-size: 11px;
    color: #888;
}

.report-actions {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #666;
}

.report-action-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.view-btn:hover {
    color: #2196F3;
    border-color: #2196F3;
}

.download-btn:hover {
    color: #4CAF50;
    border-color: #4CAF50;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}
